#!/usr/bin/env python3
"""
Test script for the simplified GAIA service.

This script tests the basic functionality of the simplified service
without running the full server.
"""

import asyncio
import sys
from typing import Dict, Any

# Mock the dependencies to avoid circular imports
class MockTask:
    def __init__(self, content, additional_info="", id=""):
        self.content = content
        self.additional_info = additional_info
        self.id = id
        self.overall_task = content

class MockWorkforce:
    def is_running(self):
        return False
    
    def stop(self):
        pass
    
    def process_task(self, task, max_replanning_tries=2):
        return {"result": "mock_answer"}
    
    def get_workforce_final_answer(self, processed_task):
        return "42"

class MockBenchmark:
    def question_scorer(self, answer, ground_truth):
        return answer == ground_truth

# Mock the models
class MockGAIAQuery:
    def __init__(self, question, level=1, final_answer="", file_attachments=None, annotator_metadata=None, additional_info=""):
        self.question = question
        self.level = level
        self.final_answer = final_answer
        self.file_attachments = file_attachments or []
        self.annotator_metadata = annotator_metadata or {}
        self.additional_info = additional_info

class MockEvaluationRequest:
    def __init__(self, query, max_replanning_tries=2, timeout_seconds=600, llm_config=None):
        self.query = query
        self.max_replanning_tries = max_replanning_tries
        self.timeout_seconds = timeout_seconds
        self.llm_config = llm_config

class MockEvaluationResult:
    def __init__(self, correct, model_answer, ground_truth, score):
        self.correct = correct
        self.model_answer = model_answer
        self.ground_truth = ground_truth
        self.score = score

# Simplified service implementation for testing
class TestGAIAEvaluationService:
    """Test version of the simplified GAIA evaluation service."""
    
    def __init__(self, workforce=None, result_cache_seconds=60):
        self.workforce = workforce or MockWorkforce()
        self.running_tasks = {}
        self.completed_results = {}
        self.result_cache_seconds = result_cache_seconds
        self.benchmark = MockBenchmark()

    async def submit_and_process_evaluation(self, request):
        """Submit and start processing a GAIA evaluation."""
        import uuid
        task_id = str(uuid.uuid4())
        
        # Start asynchronous evaluation immediately
        future = asyncio.create_task(self._process_evaluation_direct(task_id, request))
        self.running_tasks[task_id] = future
        
        print(f"Started evaluation task {task_id}")
        return task_id

    async def get_task_result(self, task_id):
        """Get task status and result."""
        import time
        
        # Check if result is cached
        if task_id in self.completed_results:
            cached_result = self.completed_results[task_id]
            cache_time = cached_result.get("_cache_time", 0)
            if time.time() - cache_time < self.result_cache_seconds:
                return cached_result["data"]
            else:
                del self.completed_results[task_id]
        
        # Check if task is running
        if task_id not in self.running_tasks:
            return {
                "task_id": task_id,
                "status": "not_found",
                "error": "Task not found"
            }
        
        future = self.running_tasks[task_id]
        
        if not future.done():
            return {
                "task_id": task_id,
                "status": "running",
                "progress": 50.0
            }
        
        # Task is completed
        try:
            result = await future
            del self.running_tasks[task_id]
            
            response_data = {
                "task_id": task_id,
                "status": "completed",
                "result": result
            }
            
            # Cache the result briefly
            self.completed_results[task_id] = {
                "data": response_data,
                "_cache_time": time.time()
            }
            
            return response_data
            
        except Exception as e:
            del self.running_tasks[task_id]
            
            error_response = {
                "task_id": task_id,
                "status": "failed",
                "error": str(e)
            }
            
            self.completed_results[task_id] = {
                "data": error_response,
                "_cache_time": time.time()
            }
            
            return error_response

    async def _process_evaluation_direct(self, task_id, request):
        """Process evaluation and return result directly."""
        try:
            print(f"Processing task {task_id}")
            
            # Simulate some processing time
            await asyncio.sleep(0.1)
            
            # Mock the evaluation result
            result = MockEvaluationResult(
                correct=True,
                model_answer="42",
                ground_truth=request.query.final_answer,
                score=1.0 if request.query.final_answer == "42" else 0.0
            )
            
            return {
                "final_answer": result.model_answer,
                "score": result.score,
                "is_correct": result.correct,
                "trajectory": [],
                "metrics": {}
            }
            
        except Exception as e:
            print(f"Error processing task {task_id}: {e}")
            raise

    def get_service_stats(self):
        """Get basic service statistics."""
        return {
            "active_tasks": len(self.running_tasks),
            "workforce_running": True
        }

async def test_simplified_service():
    """Test the simplified service functionality."""
    print("Testing simplified GAIA evaluation service...")
    
    # Create service
    service = TestGAIAEvaluationService()
    
    # Create test request
    query = MockGAIAQuery(
        question="What is the answer to life, the universe, and everything?",
        level=1,
        final_answer="42"
    )
    request = MockEvaluationRequest(query=query)
    
    # Submit task
    task_id = await service.submit_and_process_evaluation(request)
    print(f"Submitted task: {task_id}")
    
    # Check initial status (should be running)
    result = await service.get_task_result(task_id)
    print(f"Initial status: {result.get('status')}")
    
    # Wait a bit and check again (should be completed)
    await asyncio.sleep(0.2)
    result = await service.get_task_result(task_id)
    print(f"Final status: {result.get('status')}")
    
    if result.get('status') == 'completed':
        task_result = result.get('result', {})
        print(f"Final answer: {task_result.get('final_answer')}")
        print(f"Score: {task_result.get('score')}")
        print(f"Is correct: {task_result.get('is_correct')}")
    
    # Test service stats
    stats = service.get_service_stats()
    print(f"Service stats: {stats}")
    
    print("✅ Test completed successfully!")

if __name__ == "__main__":
    asyncio.run(test_simplified_service())
