"""
Simplified GAIA Evaluation Service Core

This is a simplified version that removes complex state management while
maintaining compatibility with the client polling mechanism.
"""

import asyncio
import time
from typing import Dict, Any, Optional
from uuid import uuid4

from loguru import logger

from camel.tasks import Task
from utils.enhanced_workforce import OwlGaiaWorkforce
from utils.gaia import GAIABenchmark

from .models import (
    EvaluationRequest,
    EvaluationResult,
    GAIAQuery,
)


class GAIAEvaluationService:
    """
    Simplified GAIA evaluation service with minimal state management.

    This version removes complex state tracking and focuses on direct
    request-response processing while maintaining client compatibility.
    """

    def __init__(self, workforce: Optional[OwlGaiaWorkforce] = None, result_cache_seconds: int = 60):
        """Initialize the simplified service."""
        self.workforce = workforce
        self.running_tasks: Dict[str, asyncio.Task] = {}  # Only track running tasks
        self.completed_results: Dict[str, Dict[str, Any]] = {}  # Cache results briefly
        self.result_cache_seconds = result_cache_seconds

        # Initialize benchmark for scoring
        self.benchmark = GAIABenchmark(
            data_dir="data/gaia",
            save_to="/dev/null"
        )

    async def submit_and_process_evaluation(self, request: EvaluationRequest) -> str:
        """Submit and start processing a GAIA evaluation."""
        task_id = str(uuid4())

        # Start asynchronous evaluation immediately
        future = asyncio.create_task(self._process_evaluation_direct(task_id, request))
        self.running_tasks[task_id] = future

        logger.info(f"Started evaluation task {task_id}")
        return task_id

    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """Get task status and result - simplified version with brief caching."""
        # Check if result is cached
        if task_id in self.completed_results:
            cached_result = self.completed_results[task_id]
            # Check if cache is still valid
            cache_time = cached_result.get("_cache_time", 0)
            if time.time() - cache_time < self.result_cache_seconds:
                return cached_result["data"]
            else:
                # Cache expired, remove it
                del self.completed_results[task_id]

        # Check if task is running
        if task_id not in self.running_tasks:
            return {
                "task_id": task_id,
                "status": "not_found",
                "error": "Task not found"
            }

        future = self.running_tasks[task_id]

        if not future.done():
            # Task is still running
            return {
                "task_id": task_id,
                "status": "running",
                "progress": 50.0
            }

        # Task is completed, get result and cache briefly
        try:
            result = await future
            # Clean up running task
            del self.running_tasks[task_id]

            response_data = {
                "task_id": task_id,
                "status": "completed",
                "result": result
            }

            # Cache the result briefly
            self.completed_results[task_id] = {
                "data": response_data,
                "_cache_time": time.time()
            }

            return response_data

        except Exception as e:
            # Clean up failed task
            del self.running_tasks[task_id]

            error_response = {
                "task_id": task_id,
                "status": "failed",
                "error": str(e)
            }

            # Cache the error briefly too
            self.completed_results[task_id] = {
                "data": error_response,
                "_cache_time": time.time()
            }

            return error_response

    def get_service_stats(self) -> Dict[str, Any]:
        """Get basic service statistics."""
        return {
            "active_tasks": len(self.running_tasks),
            "workforce_running": self.workforce.is_running() if hasattr(self.workforce, 'is_running') else False
        }

    async def _process_evaluation_direct(self, task_id: str, request: EvaluationRequest) -> Dict[str, Any]:
        """Process evaluation and return result directly."""
        try:
            logger.info(f"Processing task {task_id}")

            # Run the core evaluation logic
            result = await self._run_single_task(
                request.query,
                request.max_replanning_tries,
                request.llm_config
            )

            # Return result in client-expected format
            return {
                "final_answer": result.model_answer,
                "score": result.score,
                "is_correct": result.correct,
                "trajectory": [],  # Could be populated if needed
                "metrics": {}
            }

        except Exception as e:
            logger.error(f"Error processing task {task_id}: {e}")
            raise

    async def _run_single_task(self, query: GAIAQuery, max_replanning_tries: int, llm_config=None) -> EvaluationResult:
        """Run a single GAIA task - core logic only."""
        try:
            # Create CAMEL task
            camel_task = Task(
                content=query.question,
                additional_info=query.additional_info,
                id=str(uuid4())
            )
            camel_task.overall_task = query.question

            # Add file information if available
            if query.file_attachments:
                file_info = f"\n\nFile attachments: {', '.join(query.file_attachments)}"
                camel_task.additional_info = (camel_task.additional_info + file_info) if camel_task.additional_info else file_info.strip()

            # Create workforce based on LLM config
            workforce = self._get_workforce_for_config(llm_config)

            # Stop workforce if running
            if workforce.is_running():
                workforce.stop()

            # Process task with workforce
            loop = asyncio.get_event_loop()
            processed_task = await loop.run_in_executor(
                None,
                lambda: workforce.process_task(camel_task, max_replanning_tries=max_replanning_tries)
            )

            # Get the final answer
            try:
                answer = workforce.get_workforce_final_answer(processed_task)
            except Exception as e:
                logger.error(f"Error extracting final answer: {e}")
                answer = None

            # Score the answer
            score = self.benchmark.question_scorer(answer, query.final_answer)
            is_correct = score == True

            logger.info(f"Model answer: {answer}, Ground truth: {query.final_answer}, Score: {score}")

            return EvaluationResult(
                correct=is_correct,
                model_answer=answer or "",
                ground_truth=query.final_answer,
                score=1.0 if is_correct else 0.0
            )

        except Exception as e:
            logger.error(f"Error in task processing: {e}")
            return EvaluationResult(
                correct=False,
                model_answer="",
                ground_truth=query.final_answer,
                score=0.0
            )

    def _get_workforce_for_config(self, llm_config):
        """Get workforce based on LLM configuration."""
        if llm_config is None:
            raise ValueError("LLM configuration is required. This service only supports client-configured models.")

        # Import workforce factory
        from .workforce_factory import create_gaia_workforce

        # Create workforce with custom LLM config
        return create_gaia_workforce(llm_config)
