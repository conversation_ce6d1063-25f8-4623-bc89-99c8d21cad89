"""
Simplified GAIA Evaluation Service API

This provides a minimal API that maintains compatibility with the client
while removing complex state management.
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from .simplified_core import SimplifiedGAIAEvaluationService
from .models import (
    EvaluationRequest,
    GAIAQuery,
    LLMConfig
)


class SimplifiedGAIAServiceAPI:
    """Simplified FastAPI application for GAIA evaluation service."""
    
    def __init__(self, service: SimplifiedGAIAEvaluationService):
        """Initialize the API with the evaluation service."""
        self.service = service
        self.app = FastAPI(
            title="Simplified GAIA Evaluation Service",
            description="Simplified service for evaluating GAIA benchmark queries",
            version="2.0.0"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self._register_routes()
    
    def _register_routes(self):
        """Register API routes."""
        
        @self.app.get("/")
        async def root():
            """Root endpoint."""
            return {
                "service": "Simplified GAIA Evaluation Service",
                "version": "2.0.0",
                "status": "running"
            }
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            stats = self.service.get_service_stats()
            return {
                "status": "healthy",
                "version": "2.0.0",
                **stats
            }
        
        @self.app.post("/tasks")
        async def submit_task(request_data: dict):
            """
            Submit a GAIA task for evaluation.
            
            This endpoint maintains compatibility with the existing client.
            """
            try:
                # Extract data from request
                benchmark = request_data.get("benchmark")
                if benchmark != "gaia":
                    raise HTTPException(status_code=400, detail="Only GAIA benchmark is supported")
                
                model = request_data.get("model")
                params = request_data.get("params", {})
                llm_config_data = request_data.get("llm_config", {})
                
                # Create LLM config
                llm_config = LLMConfig(**llm_config_data) if llm_config_data else None
                
                # Create GAIA query from params
                gaia_query = GAIAQuery(
                    question=params.get("query", ""),
                    level=params.get("level", 1),
                    final_answer=params.get("final_answer", ""),
                    file_attachments=[params.get("file_path")] if params.get("file_path") else [],
                    annotator_metadata=params.get("annotator_metadata", {}),
                    additional_info=""
                )
                
                # Create evaluation request
                evaluation_request = EvaluationRequest(
                    query=gaia_query,
                    max_replanning_tries=params.get("max_steps", 2),
                    timeout_seconds=params.get("timeout", 600),
                    llm_config=llm_config
                )
                
                # Submit and start processing
                task_id = await self.service.submit_and_process_evaluation(evaluation_request)
                
                return {
                    "task_id": task_id,
                    "status": "pending",
                    "message": "Task submitted successfully"
                }
                
            except Exception as e:
                logger.error(f"Error submitting task: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to submit task: {str(e)}")
        
        @self.app.get("/tasks/{task_id}")
        async def get_task_result(task_id: str):
            """
            Get task status and result.
            
            This endpoint maintains compatibility with the client polling mechanism.
            """
            try:
                result = await self.service.get_task_result(task_id)
                return result
                
            except Exception as e:
                logger.error(f"Error getting task result: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to get task result: {str(e)}")


def create_simplified_service_app(workforce=None) -> FastAPI:
    """Create and configure the simplified service application."""
    service = SimplifiedGAIAEvaluationService(workforce)
    api = SimplifiedGAIAServiceAPI(service)
    return api.app
